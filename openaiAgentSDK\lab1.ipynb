import os
import asyncio
from dotenv import load_dotenv
from openai import OpenAI
from agents import Agent, trace, Runner

!pip install agents

load_dotenv(override=True)

openai_api_key = os.getenv('OPENAI_API_KEY')

if not openai_api_key:
    print("OpenAI API Key not set - please head to the troubleshooting guide in the setup folder")
    exit()
else:
    print(f"OpenAI API Key exists and begins {openai_api_key[:8]}")


