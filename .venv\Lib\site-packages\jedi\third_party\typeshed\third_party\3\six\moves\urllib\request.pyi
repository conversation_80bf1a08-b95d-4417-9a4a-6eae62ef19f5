# Stubs for six.moves.urllib.request
#
# Note: Commented out items means they weren't implemented at the time.
# Uncomment them when the modules have been added to the typeshed.
# from urllib.request import proxy_bypass as proxy_bypass
from urllib.request import (
    AbstractBasicAuth<PERSON>and<PERSON> as AbstractBasicAuth<PERSON>and<PERSON>,
    AbstractDigestAuthHandler as AbstractDigestAuthHandler,
    <PERSON><PERSON><PERSON><PERSON> as BaseHand<PERSON>,
    <PERSON>ache<PERSON><PERSON><PERSON><PERSON><PERSON> as CacheFTPHandler,
    <PERSON>cyUR<PERSON><PERSON><PERSON> as FancyURLopener,
    <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON>TPHand<PERSON>,
    HTTPBasic<PERSON>uth<PERSON><PERSON><PERSON> as HTTPBasicAuthHand<PERSON>,
    HTTPCookieProcessor as HTTPCookieProcessor,
    HTTPDefaultError<PERSON><PERSON>ler as HTTPDefaultErrorHandler,
    HTTPDigestAuthHandler as HTTPDigestAuthHandler,
    HTTPErrorProcessor as HTTPErrorProcessor,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON>TTPHand<PERSON>,
    HTTPPasswordMgr as HTTPPasswordMgr,
    HTTPPasswordMgrWithDefaultRealm as HTTPPasswordMgrWithDefaultRealm,
    HTTPRedirectHand<PERSON> as HTTPRedirectHandler,
    HTTPSHandler as HTTPSHandler,
    OpenerDirector as OpenerDirector,
    ProxyBasicAuthHandler as ProxyBasicAuthHandler,
    ProxyDigestAuthHandler as ProxyDigestAuthHandler,
    ProxyHandler as ProxyHandler,
    Request as Request,
    UnknownHandler as UnknownHandler,
    URLopener as URLopener,
    build_opener as build_opener,
    getproxies as getproxies,
    install_opener as install_opener,
    parse_http_list as parse_http_list,
    parse_keqv_list as parse_keqv_list,
    pathname2url as pathname2url,
    url2pathname as url2pathname,
    urlcleanup as urlcleanup,
    urlopen as urlopen,
    urlretrieve as urlretrieve,
)
